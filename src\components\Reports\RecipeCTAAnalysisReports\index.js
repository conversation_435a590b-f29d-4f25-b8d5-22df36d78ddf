'use client';
import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import RecipeCTAReportsTable from './RecipeCTAReportsTable';
import { staticOptions } from '@/helper/common/staticOptions';
import '../reports.scss';

export default function RecipeCTAAnalysisReports({ onFiltersUpdate }) {
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState({
    ctaType: '',
    dateRange: '',
  });
  const [paginationData, setPaginationData] = useState({
    page: 1,
    size: 10,
  });

  // Filter fields configuration
  const filterFields = [
    {
      name: 'search',
      type: 'search',
      label: 'Search by Recipe Name',
      searchclass: 'search-field-wrapper',
    },
    {
      name: 'ctaType',
      type: 'select',
      label: 'CTA Type',
      options: staticOptions?.CTA_ANALYTICS_TYPE_OPTIONS,
    },
    {
      name: 'dateRange',
      type: 'select',
      label: 'Date Range',
      options: staticOptions?.ANALYTICS_DATE_RANGES,
    },
  ];

  // Handle filter apply
  const handleFilterApply = (filterValues) => {
    setFilters(filterValues);
    setSearchValue(filterValues?.search || '');

    // Prepare filters for export with pagination
    const exportFilters = {
      search: filterValues?.search || '',
      recipe_name: filterValues?.search || '', // Map search to recipe_name
      cta_type: filterValues?.ctaType || '', // Map ctaType to cta_type
      date_range: filterValues?.dateRange || '',
      page: paginationData.page,
      size: paginationData.size,
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    if (fieldName === 'search') {
      setSearchValue(value);
    }
  };

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        search: searchValue || '',
        recipe_name: searchValue || '', // Map search to recipe_name
        cta_type: filters?.ctaType || '', // Map ctaType to cta_type
        date_range: filters?.dateRange || '',
        page: paginationData.page,
        size: paginationData.size,
      };

      onFiltersUpdate(exportFilters);
    }
  }, [filters, searchValue, paginationData]); // Added paginationData to dependencies

  return (
    <Box className="report-main-container">
      {/* Filter Section */}
      <FilterCollapse
        fields={filterFields}
        onApply={handleFilterApply}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      {/* Table Section */}
      <RecipeCTAReportsTable
        searchValue={searchValue}
        filters={filters}
        onPaginationChange={setPaginationData}
      />
    </Box>
  );
}
