'use client';
import { useState, useEffect, useContext, useCallback } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import LeavePopOver from '@/components/Leave/LeavePopOver';
import AuthContext from '@/helper/authcontext';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { leaveService } from '@/services/leaveService';
import { reportsService } from '@/services/reportService';
import { staticOptions } from '@/helper/common/staticOptions';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import useRoleList from '@/hooks/useRoleList';
import '../reports.scss';

export default function LeaveBalanceReports({ onFiltersUpdate }) {
  const { authState, AllListsData, setAllListsData, setUserdata } =
    useContext(AuthContext);
  const { roleList, fetchRoleList } = useRoleList();

  // State management
  const [loader, setLoader] = useState(false);
  const [leaveBalanceList, setLeaveBalanceList] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({ leavetype: 'day' }); // Default to 'day'
  const [searchValue, setSearchValue] = useState('');

  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });
  const [branchOptions, setBranchOptions] = useState([
    { label: 'Select Branch', value: '' },
    ...(AllListsData?.ActiveBranchList || []),
  ]);
  const [departmentOptions, setDepartmentOptions] = useState([
    { label: 'Select Department', value: '' },
    ...(AllListsData?.ActiveDepartmentList || []),
  ]);

  // Filter fields configuration (following StaffUserReports pattern)
  const filterFields = [
    {
      type: 'search',
      label: 'Search',
      name: 'search',
      placeholder: 'Search',
    },
    {
      type: 'select',
      label: 'Branch',
      name: 'branch',
      placeholder: 'Select Branch',
      options: branchOptions,
      showDot: true,
    },
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      placeholder: 'Select Department',
      options: departmentOptions,
    },
    {
      type: 'select',
      label: 'System Access',
      name: 'role',
      placeholder: 'Select Role',
      options: roleList,
    },
    {
      type: 'select',
      label: 'Leave Type',
      name: 'leavetype',
      placeholder: 'Select Leave Type',
      options: staticOptions?.LEAVE_TYPE || [],
    },
    {
      type: 'select',
      label: 'Leave Category',
      name: 'leaveType',
      placeholder: 'Select Leave Category',
      options: AllListsData?.ActiveLeaveList || [],
    },
    {
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    },
  ];

  // No action menu items needed for leave balance reports

  // CommonTable columns configuration (matching ChangeRequestReports pattern)
  const columns = [
    {
      header: 'ID',
      accessor: 'id',
      sortable: true,
      sortKey: 'id', // API sort key for ID
    },
    {
      header: 'User',
      accessor: 'user_full_name',
      sortable: false, // Complex field, keep non-sortable
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          setUserdata={setUserdata}
          authState={authState}
        />
      ),
    },
    {
      header: 'Branch / Dep.',
      accessor: 'branch?.branch_name',
      sortable: false, // Complex field, keep non-sortable
      renderCell: (_, row) => <BranchDepartmentDisplay row={row} />,
    },
    {
      header: 'Total Leaves',
      accessor: 'total_leave_days',
      sortable: false, // Complex calculated field, keep non-sortable
      renderCell: (value, row) => {
        const leaveDetails =
          row?.leave_details?.map((leave) => {
            return {
              leaveCount: leave?.leave_balance,
              leaveName: leave?.name,
              leaveUnlimited: leave?.has_leave_unlimited,
            };
          }) || [];
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Box sx={{ boxShadow: 'none' }}>
              <Typography className="title-text">
                <Tooltip
                  className="event-title text-ellipsis"
                  variant="h6"
                  interactive
                  title={
                    leaveDetails.length > 0 && (
                      <LeavePopOver
                        title="Total Leave"
                        Leavedays="Leave Type"
                        leave="No of leaves"
                        leaveDetails={leaveDetails}
                      />
                    )
                  }
                  classes={{
                    tooltip: 'leave-days-popover',
                  }}
                >
                  <span>
                    {value !== null && value !== undefined ? value : '0'}
                  </span>
                </Tooltip>
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      header: 'Used Leave',
      accessor: 'total_used_leave_days',
      sortable: false, // Complex calculated field, keep non-sortable
      renderCell: (value, row) => {
        const leaveDetails =
          row?.leave_details?.map((leave) => {
            // Format leave dates for display
            const leaveDatesText =
              leave?.leave_dates?.length > 0
                ? leave.leave_dates
                    .map((dateRange) => {
                      const startDate = new Date(
                        dateRange.start_date
                      ).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                      });
                      const endDate = new Date(
                        dateRange.end_date
                      ).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                      });
                      const dateText =
                        startDate === endDate
                          ? startDate
                          : `${startDate} - ${endDate}`;
                      return `(${dateText})`;
                    })
                    .join(' ')
                : 'No dates';

            return {
              leaveCount: leave?.used_leave?.total + 'd',
              leaveName: leave?.name,
              leaveDates: leaveDatesText,
            };
          }) || [];
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Typography className="title-text">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                title={
                  leaveDetails.length > 0 && (
                    <LeavePopOver
                      title="Total Leave"
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      leaveDetails={leaveDetails}
                    />
                  )
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>
                  {value !== null && value !== undefined ? value : '0'}
                </span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
    {
      header: 'Balance',
      accessor: 'available_balance',
      sortable: false, // Complex calculated field, keep non-sortable
      renderCell: (value, row) => {
        const leaveDetails =
          row?.leave_details?.map((leave) => {
            return {
              leaveCount: leave?.user_remaining_leave,
              leaveName: leave?.name,
              leaveUnlimited: leave?.has_leave_unlimited,
            };
          }) || [];
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Typography className="title-text">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                title={
                  leaveDetails.length > 0 && (
                    <LeavePopOver
                      title="Total Leave"
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      leaveDetails={leaveDetails}
                    />
                  )
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>
                  {value !== null && value !== undefined ? value : '0'}
                </span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
  ];

  // Get leave balance list using service
  const getLeaveBalanceList = useCallback(
    async (
      search = '',
      pageNo = 1,
      branch = '',
      role = '',
      department = '',
      Rpp = rowsPerPage,
      startDate = '',
      endDate = '',
      leaveType = '',
      leaveCategory = '',
      sortBy = '',
      sortOrderValue = '',
      showLoader = true
    ) => {
      if (showLoader) setLoader(true);
      try {
        const params = {
          search,
          page: pageNo,
          size: Rpp,
          branch_id: branch,
          department_id: department,
          role_id: role,
          start_date: startDate,
          end_date: endDate,
          leave_type_id: leaveCategory,
          sort_by: sortBy,
          sort_order: sortOrderValue,
          type: 'report',
        };

        // Only add report_mode if leaveType is actually provided
        if (leaveType) {
          params.report_mode = leaveType;
        }

        const response =
          await reportsService.getLeaveBalanceReportsList(params);

        if (response.success) {
          setLeaveBalanceList(response.data);
          setTotalCount(response.count);
        } else {
          setLeaveBalanceList([]);
          setTotalCount(0);
          setApiMessage('error', response.message);
        }
        setLoader(false);
      } catch (error) {
        setLoader(false);
        setLeaveBalanceList([]);
        setTotalCount(0);
        setApiMessage(
          'error',
          error?.message || 'Failed to fetch leave balance data'
        );
      }
    },
    [rowsPerPage]
  );

  // Load filter options using service
  const loadFilterOptions = useCallback(async () => {
    try {
      // Fetch role list using hook
      await fetchRoleList();

      const leaveTypesResponse = await leaveService.getLeaveTypesList();

      if (leaveTypesResponse.success) {
        const leaveTypeOptions =
          leaveTypesResponse.data?.map((leaveType) => ({
            label: leaveType.name,
            value: leaveType.id,
          })) || [];
        setAllListsData((prevData) => ({
          ...prevData,
          ActiveLeaveList: leaveTypeOptions,
        }));
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  }, [fetchRoleList, setAllListsData]);

  // Handle sorting
  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setPage(1);

    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Update export filters when sorting changes
    if (onFiltersUpdate) {
      const exportFilters = {};

      if (searchValue) exportFilters.search = searchValue;
      if (filters?.branch) exportFilters.branch_id = filters.branch;
      if (filters?.role) exportFilters.role_id = filters.role;
      if (filters?.department) exportFilters.department_id = filters.department;
      if (startDate) exportFilters.start_date = startDate;
      if (endDate) exportFilters.end_date = endDate;
      if (filters?.leaveType) {
        exportFilters.leave_type_id = filters.leaveType;
      }

      // Only add report_mode if leavetype filter is specifically applied
      if (filters?.leavetype) {
        exportFilters.report_mode = filters.leavetype;
      }

      // Add new sorting parameters
      exportFilters.sort_by = key;
      exportFilters.sort_order = newOrder;

      // Add pagination parameters for export
      exportFilters.page = 1; // Reset to page 1 when sorting
      exportFilters.size = rowsPerPage;
      exportFilters.totalCount = totalCount || 0;

      onFiltersUpdate(exportFilters);
    }

    // Call API with new sort order without showing loader
    getLeaveBalanceList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.leavetype || '',
      filters?.leaveType || '',
      key,
      newOrder,
      false // Don't show loader for sorting
    );
  };

  // Handle filter application
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values?.search || '');

    const startDate = values?.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values?.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Prepare filters for export - only include non-empty values
    const exportFilters = {};

    if (values?.search) exportFilters.search = values.search;
    if (values?.branch) exportFilters.branch_id = values.branch;
    if (values?.role) exportFilters.role_id = values.role;
    if (values?.department) exportFilters.department_id = values.department;
    if (startDate) exportFilters.start_date = startDate;
    if (endDate) exportFilters.end_date = endDate;
    if (values?.leaveType) {
      exportFilters.leave_type_id = values.leaveType;
    }

    // Only add report_mode if leavetype filter is specifically applied
    if (values?.leavetype) {
      exportFilters.report_mode = values.leavetype;
    }

    // Add sorting parameters for export
    if (sortOrder?.key) exportFilters.sort_by = sortOrder.key;
    if (sortOrder?.value) exportFilters.sort_order = sortOrder.value;

    // Add pagination parameters for export
    exportFilters.size = rowsPerPage; // Pass current page size for export
    exportFilters.totalCount = totalCount || 0; // Keep totalCount for reference

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }

    setPage(1);
    getLeaveBalanceList(
      values?.search || '',
      1,
      values?.branch || '',
      values?.role || '',
      values?.department || '',
      rowsPerPage,
      startDate,
      endDate,
      values?.leavetype || '',
      values?.leaveType || '',
      sortOrder?.key || '',
      sortOrder?.value || '',
      false // Don't show loader for filter operations
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getLeaveBalanceList(
      searchValue,
      newPage,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.leavetype || '',
      filters?.leaveType || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getLeaveBalanceList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      newRowsPerPage,
      startDate,
      endDate,
      filters?.leavetype || '',
      filters?.leaveType || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // useEffect hooks
  useEffect(() => {
    loadFilterOptions();
  }, []); // Empty dependency array for initial load only

  // Update branch and department options when AllListsData changes
  useEffect(() => {
    setBranchOptions([
      { label: 'Select Branch', value: '' },
      ...(AllListsData?.ActiveBranchList || []),
    ]);
    setDepartmentOptions([
      { label: 'Select Department', value: '' },
      ...(AllListsData?.ActiveDepartmentList || []),
    ]);
  }, [AllListsData?.ActiveBranchList, AllListsData?.ActiveDepartmentList]);

  useEffect(() => {
    getLeaveBalanceList();
  }, []); // Empty dependency array for initial load only

  // Update parent with initial filters on mount
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        search: '',
        branch_id: '',
        role_id: '',
        department_id: '',
        start_date: '',
        end_date: '',
        leave_type_id: '',
        report_mode: 'day', // Default to 'day' like LeaveBalance component
        page: page, // Pass current page for export
        size: rowsPerPage, // Pass current page size for export
        totalCount: totalCount || 0, // Keep totalCount for reference
      };
      onFiltersUpdate(exportFilters);
    }
  }, [onFiltersUpdate]); // Run only when onFiltersUpdate changes (component mount)

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      const startDate = filters?.dateRange?.[0]
        ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
        : '';
      const endDate = filters?.dateRange?.[1]
        ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
        : '';

      // Prepare filters for export - only include non-empty values
      const exportFilters = {};

      if (searchValue) exportFilters.search = searchValue;
      if (filters?.branch) exportFilters.branch_id = filters.branch;
      if (filters?.role) exportFilters.role_id = filters.role;
      if (filters?.department) exportFilters.department_id = filters.department;
      if (startDate) exportFilters.start_date = startDate;
      if (endDate) exportFilters.end_date = endDate;
      if (filters?.leaveType) {
        exportFilters.leave_type_id = filters.leaveType;
      }

      // Only add report_mode if leavetype filter is specifically applied
      if (filters?.leavetype) {
        exportFilters.report_mode = filters.leavetype;
      }

      // Add sorting parameters for export
      if (sortOrder?.key) exportFilters.sort_by = sortOrder.key;
      if (sortOrder?.value) exportFilters.sort_order = sortOrder.value;

      // Add pagination parameters for export
      exportFilters.page = page; // Pass current page for export
      exportFilters.size = rowsPerPage; // Pass current page size for export
      exportFilters.totalCount = totalCount || 0; // Keep totalCount for reference

      onFiltersUpdate(exportFilters);
    }
  }, [filters, searchValue, page, rowsPerPage, totalCount, sortOrder]); // Added sortOrder dependency

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
      />

      <Box className="report-table-container">
        {loader ? (
          <ContentLoader />
        ) : (
          <CommonTable
            columns={columns}
            data={leaveBalanceList}
            totalCount={totalCount}
            currentPage={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            showPagination={true}
            onSort={handleSort}
            sortOrder={sortOrder}
          />
        )}
      </Box>
    </Box>
  );
}
