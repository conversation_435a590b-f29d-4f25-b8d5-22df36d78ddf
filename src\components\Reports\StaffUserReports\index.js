'use client';
import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography } from '@mui/material';

import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { staticOptions } from '@/helper/common/staticOptions';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';

import useRoleList from '@/hooks/useRoleList';

import '../reports.scss';

// Status class functions
const getStatusClass = (status) => {
  const map = {
    rejected: 'failed',
    deleted: 'failed',
    cancelled: 'failed',
    ongoing: 'ongoing',
    pending: 'draft',
    active: 'active-onboarding',
    completed: 'active-onboarding',
    verified: 'active-onboarding',
  };
  return map[status] || 'success';
};

const getStatusClassOnboard = (status) => {
  const map = {
    ongoing: 'ongoing',
    pending: 'draft',
    completed: 'active-onboarding',
  };
  return map[status] || 'success';
};

export default function StaffUserReports({ onFiltersUpdate }) {
  const { authState, setUserdata, AllListsData } = useContext(AuthContext);
  const { roleList, fetchRoleList } = useRoleList();

  const [staffList, setStaffList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');

  // Dynamic filter options state (following RotaReports pattern)
  const [branchOptions, setBranchOptions] = useState([
    { label: 'Select Branch', value: '' },
    ...(AllListsData?.ActiveBranchList || []),
  ]);
  const [departmentOptions, setDepartmentOptions] = useState([
    { label: 'Select Department', value: '' },
    ...(AllListsData?.ActiveDepartmentList || []),
  ]);
  const [roleOptions, setRoleOptions] = useState([
    { label: 'Select Role', value: '' },
  ]);

  // Sorting state
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });

  // Dynamic filter fields (following RotaReports pattern)
  const filterFields = [
    {
      type: 'search',
      label: 'Search',
      name: 'search',
      placeholder: 'Search by name, email, or employment number',
    },
    {
      type: 'select',
      label: 'Branch',
      name: 'branch',
      placeholder: 'Select Branch',
      options: branchOptions,
    },
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      placeholder: 'Select Department',
      options: departmentOptions,
    },
    {
      type: 'select',
      label: 'Role',
      name: 'role',
      placeholder: 'Select Role',
      options: roleOptions,
    },
    {
      type: 'select',
      label: 'Status',
      name: 'status',
      placeholder: 'Select Status',
      options: staticOptions.USER_STATUS_OPTIONS,
    },
    {
      type: 'select',
      label: 'Invitation Status',
      name: 'invitationStatus',
      placeholder: 'Select Invitation Status',
      options: staticOptions.INVITE_STATUS,
    },
    {
      type: 'select',
      label: 'Training Status',
      name: 'trainingStatus',
      placeholder: 'Select Training Status',
      options: staticOptions.TRAINING_FILTER_STATUS,
    },
    {
      type: 'select',
      label: 'Contract Status',
      name: 'contractStatus',
      placeholder: 'Select Contract Status',
      options: staticOptions.CONTRACT_FILTER_STATUS,
    },
    {
      type: 'select',
      label: 'Employee Type',
      name: 'filter_type',
      placeholder: 'Select Employee Type',
      options: [
        { label: 'Joining Date', value: 'joining_date' },
        { label: 'Old Employee', value: 'old_employee' },
        { label: 'Notice Period', value: 'notice_period' },
      ],
    },
    {
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
      conditional: {
        dependsOn: 'filter_type',
        showWhen: ['joining_date', 'notice_period'],
      },
    },
  ];

  // CommonTable columns
  const columns = [
    {
      header: 'ID',
      accessor: 'employment_number',
      sortable: true,
      sortKey: 'employment_number', // API sort key for employment_number
      renderCell: (value) => (
        <Box className="d-flex align-center justify-start h100">
          {value ? value : '-'}
        </Box>
      ),
    },
    {
      header: 'User',
      accessor: 'user_full_name',
      sortable: false,
      sortKey: 'user_full_name', // API sort key for user name
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          setUserdata={setUserdata}
          authState={authState}
          navigationProps={{ staff: true }}
        />
      ),
    },
    {
      header: 'Branch / Dep.',
      accessor: 'branch_department',
      sortable: false, // Complex field, keep non-sortable
      renderCell: (_, row) => <BranchDepartmentDisplay row={row} />,
    },
    {
      header: 'Joining Date',
      accessor: 'user_joining_date',
      sortable: false,
      sortKey: 'user_joining_date', // API sort key for joining date
      renderCell: (value) => (
        <Box className="d-flex align-center justify-start h100">
          {DateFormat(value, 'date')}
        </Box>
      ),
    },
    {
      header: 'Profile Status',
      accessor: 'user_status',
      sortable: false,
      sortKey: 'user_status', // API sort key for user status
      renderCell: (value) => (
        <Box className="d-flex align-center justify-start h100 text-capital">
          <Typography
            className={`sub-title-text ${getStatusClass(value)} fw600`}
          >
            {value}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Training Status',
      accessor: 'user_track_status',
      sortable: false,
      sortKey: 'user_track_status', // API sort key for training status
      renderCell: (value) => (
        <Box className="d-flex align-center justify-start h100 text-capital">
          <Typography
            className={`sub-title-text ${getStatusClassOnboard(value)} fw600`}
          >
            {value}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Contract Status',
      accessor: 'user_contract',
      sortable: false, // Complex calculated field, keep non-sortable
      renderCell: (_, row) => {
        const contractStatus =
          row?.user_contract?.is_confirm_sign === 1
            ? row?.is_probation === 1
              ? 'Probation'
              : 'Active'
            : row?.user_contract?.is_confirm_sign === 0
              ? 'Awaiting Signature'
              : 'Pending';

        return (
          <Box className="d-flex align-center justify-start h100 text-capital">
            <Typography
              className={`sub-title-text ${getStatusClass(contractStatus?.toLowerCase())} fw600`}
            >
              {contractStatus}
            </Typography>
          </Box>
        );
      },
    },
  ];

  // Load filter options (following RotaReports pattern)
  const loadFilterOptions = async () => {
    try {
      // Fetch role list using hook
      await fetchRoleList();
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  // Get staff list from API
  const getStaffList = async (
    search = '',
    pageNo = 1,
    branch = '',
    role = '',
    department = '',
    statusValue = '',
    trainingStatus = '',
    contractStatus = '',
    invitationStatus = '',
    Rpp = rowsPerPage,
    startDate = '',
    endDate = '',
    filterType = '',
    sortBy = '',
    sortOrderValue = '',
    showLoader = true
  ) => {
    if (showLoader) setLoader(true);
    try {
      let url =
        URLS?.GET_USER_LIST +
        `?isAdmin=false&search=${search}&page=${pageNo}&size=${Rpp}&branch_id=${branch}&status=${statusValue}&user_track_status=${trainingStatus}&contract_status=${contractStatus}&invitation_status=${invitationStatus}&role_id=${role}&department_id=${department}`;

      // Add filter_type if provided
      if (filterType) url += `&filter_type=${filterType}`;

      // Add date range if provided
      if (startDate) url += `&start_date=${startDate}`;
      if (endDate) url += `&end_date=${endDate}`;

      // Add sorting parameters if provided
      if (sortBy) url += `&sort_by=${sortBy}`;
      if (sortOrderValue) url += `&sort_order=${sortOrderValue}`;

      const { status, data } = await axiosInstance.get(url);

      if (status === 200) {
        setStaffList(data?.userList || []);
        setTotalCount(data?.count || 0);
        setPage(pageNo);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setStaffList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handle filter application
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values?.search || '');

    const startDate = values?.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values?.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Prepare filters for export
    const exportFilters = {
      search: values?.search || '',
      branch: values?.branch || '',
      role: values?.role || '',
      department: values?.department || '',
      status: values?.status || '',
      trainingStatus: values?.trainingStatus || '',
      contractStatus: values?.contractStatus || '',
      invitationStatus: values?.invitationStatus || '',
      filterType: values?.filter_type || '',
      sortBy: sortOrder?.key || '',
      sortOrder: sortOrder?.value || '',
      startDate,
      endDate,
      size: rowsPerPage, // Pass current page size for export
      totalCount: totalCount || 0, // Keep totalCount for reference
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }

    setPage(1);
    getStaffList(
      values?.search || '',
      1,
      values?.branch || '',
      values?.role || '',
      values?.department || '',
      values?.status || '',
      values?.trainingStatus || '',
      values?.contractStatus || '',
      values?.invitationStatus || '',
      rowsPerPage,
      startDate,
      endDate,
      values?.filter_type || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getStaffList(
      searchValue,
      newPage,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      filters?.invitationStatus || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.filter_type || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getStaffList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      filters?.invitationStatus || '',
      newRowsPerPage,
      startDate,
      endDate,
      filters?.filter_type || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Handle sorting
  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setPage(1);

    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Call API with new sort order without showing loader
    getStaffList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      filters?.invitationStatus || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.filter_type || '',
      key,
      newOrder,
      false // Don't show loader for sorting
    );
  };

  // useEffect hooks (following RotaReports pattern)
  useEffect(() => {
    // Use AllListsData for branches if available
    if (AllListsData?.ActiveBranchList) {
      setBranchOptions([
        { label: 'Select Branch', value: '' },
        ...AllListsData.ActiveBranchList,
      ]);
    }
  }, [AllListsData?.ActiveBranchList]);

  useEffect(() => {
    // Use AllListsData for departments if available
    if (AllListsData?.ActiveDepartmentList) {
      setDepartmentOptions([
        { label: 'Select Department', value: '' },
        ...AllListsData.ActiveDepartmentList,
      ]);
    }
  }, [AllListsData?.ActiveDepartmentList]);

  useEffect(() => {
    // Use roleList from hook
    if (roleList && roleList.length > 0) {
      setRoleOptions([{ label: 'Select Role', value: '' }, ...roleList]);
    }
  }, [roleList]);

  useEffect(() => {
    loadFilterOptions();
  }, []);

  useEffect(() => {
    getStaffList();
  }, []);

  // Update parent with initial filters on mount
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        search: '',
        branch: '',
        role: '',
        department: '',
        status: '',
        trainingStatus: '',
        contractStatus: '',
        invitationStatus: '',
        filterType: '',
        sortBy: '',
        sortOrder: '',
        startDate: '',
        endDate: '',
        size: rowsPerPage, // Pass current page size for export
        totalCount: totalCount || 0, // Keep totalCount for reference
      };
      onFiltersUpdate(exportFilters);
    }
  }, [onFiltersUpdate]); // Run only when onFiltersUpdate changes (component mount)

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      const startDate = filters?.dateRange?.[0]
        ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
        : '';
      const endDate = filters?.dateRange?.[1]
        ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
        : '';

      const exportFilters = {
        search: searchValue || '',
        branch: filters?.branch || '',
        role: filters?.role || '',
        department: filters?.department || '',
        status: filters?.status || '',
        trainingStatus: filters?.trainingStatus || '',
        contractStatus: filters?.contractStatus || '',
        invitationStatus: filters?.invitationStatus || '',
        filterType: filters?.filter_type || '',
        sortBy: sortOrder?.key || '',
        sortOrder: sortOrder?.value || '',
        startDate,
        endDate,
        size: rowsPerPage, // Pass current page size for export
        totalCount: totalCount || 0, // Keep totalCount for reference
      };

      onFiltersUpdate(exportFilters);
    }
  }, [filters, searchValue, totalCount, sortOrder]); // Include sortOrder in dependencies

  return (
    <Box className="report-main-container">
      <>
        <FilterCollapse
          fields={filterFields}
          onApply={handleApplyFilters}
          initialValues={filters}
        />

        <Box className="report-table-container">
          {loader ? (
            <ContentLoader />
          ) : (
            <CommonTable
              columns={columns}
              data={staffList}
              totalCount={totalCount}
              currentPage={page}
              rowsPerPage={rowsPerPage}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              showPagination={true}
              onSort={handleSort}
              sortOrder={sortOrder}
              actionMenuItems={[]}
            />
          )}
        </Box>
      </>
    </Box>
  );
}
