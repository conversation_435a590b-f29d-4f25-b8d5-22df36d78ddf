'use client';
import React, { useEffect, useState, useContext } from 'react';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { reportsService } from '@/services/reportService';
import AuthContext from '@/helper/authcontext';
import moment from 'moment';
import RotaReportDetails from './RotaReportDetails';
import { staticOptions } from '@/helper/common/staticOptions';

const columns = [
  { header: 'Employee ID', accessor: 'employment_number', sortable: true },
  { header: 'User Name', accessor: 'user_full_name', sortable: false },
  { header: 'Branch', accessor: 'branch', sortable: false },
  { header: 'Department', accessor: 'department', sortable: false },
  { header: 'Total Shifts', accessor: 'total_shifts', sortable: true },
  { header: 'Total Hours', accessor: 'total_hours', sortable: true },
  { header: 'Total Breaks', accessor: 'total_break_hours', sortable: true },
];

export default function RotaReports({ onFiltersUpdate }) {
  const { AllListsData } = useContext(AuthContext);
  const [filters, setFilters] = useState({ date_period: 'month' });
  const [filteredData, setFilteredData] = useState([]);
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Details view state
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // Dynamic filter options state
  const [branchOptions, setBranchOptions] = useState([
    { label: 'Select Branch', value: '' },
    ...AllListsData.ActiveBranchList,
  ]);
  const [departmentOptions, setDepartmentOptions] = useState([
    { label: 'Select Department', value: '' },
  ]);

  const [loading, setLoading] = useState(false);

  var weekStart = moment(new Date())
    .clone()
    .startOf('isoWeek')
    .format('YYYY-MM-DD');
  var weekEnd = moment(new Date())
    .clone()
    .endOf('isoWeek')
    .format('YYYY-MM-DD');

  var firstDay = moment(new Date()).startOf('month').format('YYYY-MM-DD');
  var lastDay = moment(new Date()).endOf('month').format('YYYY-MM-DD');

  useEffect(() => {
    // Use AllListsData for branches if available
    if (AllListsData?.ActiveBranchList) {
      setBranchOptions([
        { label: 'Select Branch', value: '' },
        ...AllListsData.ActiveBranchList,
      ]);
    }
  }, [AllListsData.ActiveBranchList]);

  useEffect(() => {
    setDepartmentOptions([
      { label: 'Select Department', value: '' },
      ...AllListsData?.ActiveDepartmentList,
    ]);
  }, [AllListsData?.ActiveDepartmentList]);

  const fetchRotaReportsList = async (
    filterParams = '',
    page = currentPage,
    size = rowsPerPage,
    sortBy = '',
    sortOrderValue = ''
  ) => {
    setLoading(true);
    try {
      // If no filter params provided, use current month as default
      let params = filterParams;
      if (!params) {
        if (filters.date_period === 'week') {
          params = `?from=${weekStart}&to=${weekEnd}`;
        } else if (filters.date_period === 'month') {
          params = `?from=${firstDay}&to=${lastDay}`;
        }
      }

      // Add pagination parameters
      const separator = params.includes('?') ? '&' : '?';
      const paginationParams = `${separator}page=${page}&size=${size}&sortBy=${sortBy}&sortOrder=${sortOrderValue}`;
      const finalParams = params + paginationParams;

      const data = await reportsService.getRotaReportsList(finalParams);
      setFilteredData(data?.data || []);
      setTotalCount(data?.count || 0);
    } catch (error) {
      console.error('Error fetching rota reports:', error);
      setFilteredData([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchRotaReportsList();
  }, []);

  // Update parent with initial filters on mount
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        search: '',
        branch: '',
        department: '',
        user: '',
        startDate: firstDay,
        endDate: lastDay,
        size: rowsPerPage, // Pass current page size for export
        totalCount: totalCount || 0, // Keep totalCount for reference
      };
      onFiltersUpdate(exportFilters);
    }
  }, [onFiltersUpdate]); // Run only when onFiltersUpdate changes (component mount)

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      // Calculate date range for export
      let startDate, endDate;
      if (filters.date_period === 'week') {
        startDate = weekStart;
        endDate = weekEnd;
      } else if (filters.date_period === 'month') {
        startDate = firstDay;
        endDate = lastDay;
      } else if (
        filters.date_period === 'custom' &&
        filters.dateRange &&
        filters.dateRange.length === 2
      ) {
        startDate = new Date(filters.dateRange[0]).toISOString().split('T')[0];
        endDate = new Date(filters.dateRange[1]).toISOString().split('T')[0];
      } else {
        // Default to current month
        startDate = firstDay;
        endDate = lastDay;
      }

      const exportFilters = {
        search: filters?.search || '',
        branch: filters?.branch || '',
        department: filters?.department || '',
        user: filters?.user || '',
        startDate,
        endDate,
        size: rowsPerPage, // Pass current page size for export
        totalCount: totalCount || 0, // Keep totalCount for reference
      };

      onFiltersUpdate(exportFilters);
    }
  }, [filters, totalCount, rowsPerPage]); // Include dependencies

  // Dynamically build filter fields
  const filterFields = [
    {
      type: 'search',
      label: 'Search',
      name: 'search',
      placeholder: 'Enter Search',
    },
    {
      type: 'select',
      label: 'Branch',
      name: 'branch',
      options: branchOptions,
      placeholder: 'Select Branch',
    },
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      options: departmentOptions,
      placeholder: 'Select Department',
    },
    {
      type: 'select',
      label: 'Date',
      name: 'date_period',
      options: staticOptions?.PERIOD_OPTIONS,
      placeholder: 'Select Date',
    },
  ];

  // Insert date-range field if date_period is 'custom'
  if (filters.date_period === 'custom') {
    filterFields.push({
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    });
  }

  // Build API query parameters from filters
  const buildFilterParams = (
    filterValues
    // page = currentPage,
    // size = rowsPerPage
  ) => {
    const params = new URLSearchParams();

    // Handle date range
    let startDate, endDate;
    if (filterValues.date_period === 'week') {
      startDate = weekStart;
      endDate = weekEnd;
    } else if (filterValues.date_period === 'month') {
      startDate = firstDay;
      endDate = lastDay;
    } else if (
      filterValues.date_period === 'custom' &&
      filterValues.dateRange &&
      filterValues.dateRange.length === 2
    ) {
      startDate = filterValues.dateRange[0]
        ? moment(new Date(filterValues.dateRange[0])).format('YYYY-MM-DD')
        : firstDay;
      endDate = filterValues.dateRange[1]
        ? moment(new Date(filterValues.dateRange[1])).format('YYYY-MM-DD')
        : lastDay;
    } else {
      // Default to current month
      startDate = firstDay;
      endDate = lastDay;
    }

    params.append('from', startDate);
    params.append('to', endDate);

    // Add pagination parameters
    // params.append('page', page);
    // params.append('size', size);

    // Add other filters if they have values
    if (filterValues.search) params.append('search', filterValues.search);
    if (filterValues.branch) params.append('branchId', filterValues.branch);
    if (filterValues.department)
      params.append('departmentId', filterValues.department);

    return '?' + params.toString();
  };

  // Handle filter changes
  const handleApplyFilters = (values) => {
    let newFilters = { ...values };
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change

    // Calculate date range for export
    let startDate, endDate;
    if (newFilters.date_period === 'week') {
      startDate = weekStart;
      endDate = weekEnd;
    } else if (newFilters.date_period === 'month') {
      startDate = firstDay;
      endDate = lastDay;
    } else if (
      newFilters.date_period === 'custom' &&
      newFilters.dateRange &&
      newFilters.dateRange.length === 2
    ) {
      startDate = new Date(newFilters.dateRange[0]).toISOString().split('T')[0];
      endDate = new Date(newFilters.dateRange[1]).toISOString().split('T')[0];
    } else {
      // Default to current month
      startDate = firstDay;
      endDate = lastDay;
    }

    // Prepare filters for export
    const exportFilters = {
      search: newFilters?.search || '',
      branch: newFilters?.branch || '',
      department: newFilters?.department || '',
      user: newFilters?.user || '',
      startDate,
      endDate,
      size: rowsPerPage, // Pass current page size for export
      totalCount: totalCount || 0, // Keep totalCount for reference
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }

    // Build API parameters and fetch data
    const filterParams = buildFilterParams(newFilters, 1, rowsPerPage);
    fetchRotaReportsList(
      filterParams,
      1,
      rowsPerPage,
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    setSortOrder({ key, value: newOrder });
    setCurrentPage(1);
    const filterParams = buildFilterParams(filters, 1, rowsPerPage);
    fetchRotaReportsList(filterParams, 1, rowsPerPage, key, newOrder);
  };
  // Handle field change (especially for date_period and branch)
  const handleFieldChange = (name, value) => {
    if (name === 'date_period') {
      let updatedFilters = { ...filters, date_period: value };
      if (value === 'custom') {
        updatedFilters.dateRange = [null, null];
      } else if (value === 'week') {
        updatedFilters.dateRange = [weekStart, weekEnd];
      } else if (value === 'month') {
        updatedFilters.dateRange = [firstDay, lastDay];
      }
      setFilters(updatedFilters);
    } else {
      setFilters((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    const filterParams = buildFilterParams(filters, newPage, rowsPerPage);
    fetchRotaReportsList(
      filterParams,
      newPage,
      rowsPerPage,
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to first page when page size changes
    const filterParams = buildFilterParams(filters, 1, newRowsPerPage);
    fetchRotaReportsList(
      filterParams,
      1,
      newRowsPerPage,
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Details view handlers
  const handleViewDetails = (row) => {
    setSelectedEmployee(row);
    setShowDetails(true);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedEmployee(null);
  };

  // Menu items for each row
  const menuItems = [
    {
      label: 'View',
      icon: <Icon name="Eye" size={16} />,
      onClick: (item, row) => {
        handleViewDetails(row);
      },
    },
  ];

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />
      {/* Your report content goes here */}

      <Box className="report-table-container">
        <CommonTable
          columns={columns}
          data={filteredData}
          actionMenuItems={menuItems}
          loading={loading}
          currentPage={currentPage}
          totalCount={totalCount}
          pageSize={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          onSort={handleSort}
          sortOrder={sortOrder}
        />
      </Box>

      {/* Details View Modal */}
      <RotaReportDetails
        employeeData={selectedEmployee}
        isOpen={showDetails}
        onClose={handleCloseDetails}
      />
    </Box>
  );
}
