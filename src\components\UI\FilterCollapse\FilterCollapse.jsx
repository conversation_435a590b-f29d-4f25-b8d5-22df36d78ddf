'use client';
import React, { useState } from 'react';
import CustomDateRangePicker from '../CustomDateRangePicker';
import CustomSelect from '../CustomSelect';
import CustomTextField from '../CustomTextField';
import CustomButton from '../CustomButton';
import CustomSearch from '../CustomSearch';
import { Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';
import './FilterCollapse.scss';

const FilterCollapse = ({
  fields = [],
  onApply,
  initialValues = {},
  onFieldChange,
  children,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [values, setValues] = useState(initialValues);

  const handleFieldChange = (name, value) => {
    setValues((prev) => {
      const newValues = { ...prev, [name]: value };

      // Clear conditional fields when their dependent field changes
      fields?.forEach((field) => {
        if (field?.conditional && field.conditional.dependsOn === name) {
          // Support both single value and array of values for showWhen
          const { showWhen } = field.conditional;
          const shouldShow = Array.isArray(showWhen)
            ? showWhen.includes(value)
            : value === showWhen;

          // If the dependent field no longer matches the showWhen condition, clear the conditional field
          if (!shouldShow) {
            newValues[field.name] =
              field.type === 'date-range' ? [null, null] : '';
          }
        }
      });

      return newValues;
    });

    if (onFieldChange) {
      onFieldChange(name, value);
    }
  };

  const handleApply = () => {
    if (onApply) onApply(values);
  };

  const handleClear = () => {
    const cleared = {};
    // Reset all field values to their default empty state
    fields?.forEach((field) => {
      if (field.type === 'date-range') {
        cleared[field.name] = [null, null];
      } else {
        cleared[field.name] = '';
      }
    });
    setValues(cleared);
    if (onApply) onApply(cleared);
  };

  return (
    <div className="filter-collapse-container">
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded((prev) => !prev)}
        className="filter-collapse-accordion"
        elevation={0}
      >
        <AccordionSummary
          className="filter-collapse-header"
          expandIcon={<ExpandMoreIcon />}
        >
          <span className="filter-collapse-icon">
            <FilterListIcon />
          </span>
          <span className="filter-collapse-title">Filters</span>
        </AccordionSummary>
        <AccordionDetails className="filter-collapse-accordion-details">
          <div className="filter-collapse-fields">
            <div className="filter-fields-row">
              {fields?.map((field) => {
                // Check if field should be conditionally displayed
                if (field?.conditional) {
                  const { dependsOn, showWhen } = field.conditional;
                  const dependentValue = values?.[dependsOn];

                  // Support both single value and array of values for showWhen
                  const shouldShow = Array.isArray(showWhen)
                    ? showWhen.includes(dependentValue)
                    : dependentValue === showWhen;

                  // Only show field if the dependent field has the required value
                  if (!shouldShow) {
                    return null;
                  }
                }

                if (field?.type === 'date-range') {
                  return (
                    <div className="filter-field" key={field?.name}>
                      <CustomDateRangePicker
                        label={field?.label}
                        value={values?.[field?.name] || [null, null]}
                        onChange={(range) =>
                          handleFieldChange(field?.name, range)
                        }
                        placeholder={field?.placeholder}
                        disabled={field?.disabled}
                        format={field?.format}
                      />
                    </div>
                  );
                }
                if (field?.type === 'select') {
                  return (
                    <div className="filter-field" key={field?.name}>
                      <CustomSelect
                        label={field?.label}
                        placeholder={
                          field?.placeholder || `Select ${field?.label}`
                        }
                        options={field?.options || []}
                        value={
                          field?.options?.find(
                            (opt) => opt?.value === values?.[field?.name]
                          ) || ''
                        }
                        onChange={(opt) =>
                          handleFieldChange(field?.name, opt?.value)
                        }
                        name={field?.name}
                      />
                    </div>
                  );
                }
                if (field?.type === 'text') {
                  return (
                    <div className="filter-field" key={field?.name}>
                      <CustomTextField
                        fullWidth
                        label={field?.label}
                        placeholder={field?.placeholder || field?.label}
                        value={values?.[field?.name] || ''}
                        onChange={(e) =>
                          handleFieldChange(field?.name, e?.target?.value)
                        }
                        name={field?.name}
                      />
                    </div>
                  );
                }
                if (field?.type === 'search') {
                  return (
                    <div className="filter-field" key={field?.name}>
                      <CustomSearch
                        searchValue={values?.[field?.name] || ''}
                        setSearchValue={(val) =>
                          handleFieldChange(field?.name, val)
                        }
                        searchclass={field?.searchclass}
                        isClearSearch={true}
                        handleClearSearch={() =>
                          handleFieldChange(field?.name, '')
                        }
                        label={field?.label}
                      />
                    </div>
                  );
                }
                return null;
              })}
            </div>
            {children}
            <div className="form-actions-btn">
              <CustomButton
                title="Apply"
                variant="contained"
                onClick={handleApply}
              />
              <CustomButton
                title="Clear"
                variant="outlined"
                onClick={handleClear}
                // style={{ marginLeft: 8 }}
              />
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};

export default FilterCollapse;
