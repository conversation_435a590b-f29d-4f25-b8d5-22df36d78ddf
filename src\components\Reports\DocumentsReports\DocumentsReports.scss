@import '@/styles/variable.scss';

body {
  .documents-reports {
    &__summary {
      margin-bottom: var(--spacing-xl);

      &-grid {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
        align-items: center;
      }

      &-item {
        padding: var(--spacing-lg);
        background-color: var(--color-secondary);
        border-radius: var(--border-radius-lg);
        border: var(--normal-sec-border);
        box-shadow: var(--box-shadow-xs);
        width: calc(25% - 12px);
        min-width: 150px;
        text-align: center;

        @media (max-width: 768px) {
          width: calc(50% - 12px);
        }

        @media (max-width: 559px) {
          width: 100%;
        }
        &-label {
          font-family: var(--font-family-primary);
          color: var(--text-color-black);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-sm);
          margin-bottom: var(--spacing-xs);
          text-transform: capitalize;
        }

        &-value {
          font-family: var(--font-family-primary);
          color: var(--text-color-primary);
          font-size: var(--font-size-base);
          line-height: var(--line-height-base);
          font-weight: var(--font-weight-semibold);
        }
      }
    }

    &__file-type {
      &-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
        text-transform: capitalize;
        display: inline-block;
      }

      &--image {
        background-color: var(--color-success-opacity);
        color: var(--color-success);
      }

      &--video {
        background-color: var(--color-primary-opacity);
        color: var(--color-primary);
      }

      &--pdf {
        background-color: var(--color-danger-opacity);
        color: var(--color-danger);
      }

      &--audio {
        background-color: var(--color-warning-opacity);
        color: var(--color-warning);
      }

      &--default {
        background-color: var(--color-light-success-opacity);
        color: var(--color-light-success);
      }
    }
  }
}
