'use client';
import React, { useState } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Card,
  CardContent,
  Grid,
  Divider,
} from '@mui/material';
import { Person as PersonIcon } from '@mui/icons-material';
import ContentLoader from '@/components/UI/ContentLoader';
import CustomTabs from '@/components/UI/CustomTabs';
import DialogBox from '@/components/UI/Modalbox';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';

import { statusClassName } from '@/helper/common/commonFunctions';
import './RotaReportDetails.scss';

const RotaReportDetails = ({ employeeData, onClose, isOpen }) => {
  const [activeTab, setActiveTab] = useState('shifts');
  const [loading] = useState(false); // Keep for table loading state

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Sorting states
  const [sortField, setSortField] = useState('');
  const [sortOrder, setSortOrder] = useState('asc');

  // Filter states
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    status: '',
    department: '',
    type: 'all', // all, shifts, swaps, drops
    date_period: 'month',
  });

  // Applied filters state - only updates when Apply button is clicked
  const [appliedFilters, setAppliedFilters] = useState({
    dateFrom: '',
    dateTo: '',
    status: '',
    department: '',
    type: 'all',
    date_period: 'month',
  });

  // Static data based on your final response structure
  const staticEmployeeData = {
    user: {
      id: '1',
      name: 'Sarah Johnson',
      role: 'Senior Nurse',
      department: 'Emergency Department',
      employeeId: 'EMP-001',
      joinDate: '2022-01-15',
      avatar: 'path/to/avatar.jpg',
    },
    quickStats: {
      totalShifts: 24,
      completedShifts: 22,
      totalHours: 176,
      leaveDays: 3,
      daysOff: 8,
      overtimeHours: 12,
    },
    shifts: [
      {
        id: '1',
        date: '2024-01-15',
        startTime: '08:00',
        endTime: '16:00',
        department: 'Emergency',
        status: 'completed',
        hours: 8,
        canDrop: false,
        swappedWith: null,
        notes: 'Regular shift',
      },
      {
        id: '2',
        date: '2024-01-16',
        startTime: '08:00',
        endTime: '16:00',
        department: 'Emergency',
        status: 'swapped',
        hours: 8,
        canDrop: false,
        swappedWith: 'John Smith',
        notes: 'Swapped with John Smith',
      },
      {
        id: '3',
        date: '2024-01-17',
        startTime: '16:00',
        endTime: '00:00',
        department: 'ICU',
        status: 'scheduled',
        hours: 8,
        canDrop: true,
        swappedWith: null,
        notes: 'Night shift',
      },
      {
        id: '4',
        date: '2024-01-18',
        startTime: '12:00',
        endTime: '20:00',
        department: 'Emergency',
        status: 'completed',
        hours: 8,
        canDrop: false,
        swappedWith: null,
        notes: 'Afternoon shift',
      },
      {
        id: '5',
        date: '2024-01-19',
        startTime: '08:00',
        endTime: '16:00',
        department: 'ICU',
        status: 'cancelled',
        hours: 8,
        canDrop: true,
        swappedWith: null,
        notes: 'Cancelled due to illness',
      },
    ],
    leaves: [
      {
        id: '1',
        startDate: '2024-01-20',
        endDate: '2024-01-22',
        type: 'annual',
        status: 'approved',
        days: 3,
        reason: 'Family vacation',
        appliedDate: '2024-01-10',
        approvedBy: { name: 'Manager Smith' },
      },
      {
        id: '2',
        startDate: '2024-02-05',
        endDate: '2024-02-05',
        type: 'sick',
        status: 'approved',
        days: 1,
        reason: 'Medical appointment',
        appliedDate: '2024-02-04',
        approvedBy: { name: 'Manager Smith' },
      },
      {
        id: '3',
        startDate: '2024-03-15',
        endDate: '2024-03-17',
        type: 'personal',
        status: 'pending',
        days: 3,
        reason: 'Personal matters',
        appliedDate: '2024-03-01',
        approvedBy: null,
      },
    ],
    daysOff: [
      {
        id: '1',
        date: '2024-01-13',
        type: 'weekend',
        status: 'confirmed',
        isRecurring: true,
        notes: 'Regular weekend off',
      },
      {
        id: '2',
        date: '2024-01-14',
        type: 'weekend',
        status: 'confirmed',
        isRecurring: true,
        notes: 'Regular weekend off',
      },
      {
        id: '3',
        date: '2024-01-01',
        type: 'holiday',
        status: 'confirmed',
        isRecurring: false,
        notes: 'New Year Day',
      },
      {
        id: '4',
        date: '2024-02-14',
        type: 'personal',
        status: 'requested',
        isRecurring: false,
        notes: 'Personal day off',
      },
    ],
  };

  // Tab configuration
  const tabs = [
    { id: 'shifts', label: 'Shifts' },
    { id: 'leaves', label: 'Leaves' },
    { id: 'daysoff', label: 'Days Off' },
  ];

  // Column configurations for CommonTable
  const shiftsColumns = [
    {
      header: 'Date',
      accessor: 'date',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Time',
      accessor: 'startTime',
      sortable: false,
      renderCell: (_, row) => {
        return `${formatTime(row.startTime)} - ${formatTime(row.endTime)}`;
      },
    },
    {
      header: 'Department',
      accessor: 'department',
      sortable: true,
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      renderCell: (value) => (
        <span className={statusClassName(value)}>{value}</span>
      ),
    },
    {
      header: 'Hours',
      accessor: 'hours',
      sortable: true,
      renderCell: (value) => `${value || 0} hours`,
    },
    {
      header: 'Swapped With',
      accessor: 'swappedWith',
      sortable: false,
      renderCell: (value, row) => {
        if (value && row.status === 'swapped') {
          return (
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ width: 24, height: 24 }}>{value.charAt(0)}</Avatar>
              <Typography variant="body2">{value}</Typography>
            </Box>
          );
        }
        return 'N/A';
      },
    },
    {
      header: 'Can Drop',
      accessor: 'canDrop',
      sortable: false,
      renderCell: (value) => (value ? 'Yes' : 'No'),
    },
    {
      header: 'Notes',
      accessor: 'notes',
      sortable: false,
      renderCell: (value) => value || 'N/A',
    },
  ];

  const leavesColumns = [
    {
      header: 'Type',
      accessor: 'type',
      sortable: true,
      renderCell: (value) => (
        <span className="annual-text">
          {value === 'annual'
            ? 'Annual Leave'
            : value === 'sick'
              ? 'Sick Leave'
              : value}
        </span>
      ),
    },
    {
      header: 'Start Date',
      accessor: 'startDate',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'End Date',
      accessor: 'endDate',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Days',
      accessor: 'days',
      sortable: true,
      renderCell: (value) => `${value || 0} day${value !== 1 ? 's' : ''}`,
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      renderCell: (value) => (
        <span className={statusClassName(value)}>{value}</span>
      ),
    },
    {
      header: 'Reason',
      accessor: 'reason',
      sortable: false,
    },
    {
      header: 'Applied Date',
      accessor: 'appliedDate',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Approved By',
      accessor: 'approvedBy',
      sortable: false,
      renderCell: (_, row) => row.approvedBy?.name || 'Pending',
    },
  ];

  const daysOffColumns = [
    {
      header: 'Date',
      accessor: 'date',
      sortable: true,
      renderCell: (value) => formatDate(value),
    },
    {
      header: 'Type',
      accessor: 'type',
      sortable: true,
      // renderCell: (value) => (
      //   <span className="default-d-text">
      //     {value === 'weekend'
      //       ? 'Weekend'
      //       : value === 'holiday'
      //         ? 'Holiday'
      //         : value}
      //   </span>
      // ),
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      renderCell: (value) => (
        <span className={statusClassName(value)}>{value}</span>
      ),
    },
    {
      header: 'Recurring',
      accessor: 'isRecurring',
      sortable: true,
      renderCell: (value) => (value ? 'Yes' : 'No'),
    },
    {
      header: 'Notes',
      accessor: 'notes',
      sortable: false,
      renderCell: (value) => value || 'N/A',
    },
  ];

  // Filter fields configuration for FilterCollapse
  const filterFields = [
    {
      name: 'type',
      label: 'Type',
      type: 'select',
      options: [
        { value: 'all', label: 'All' },
        { value: 'shifts', label: 'Shifts' },
        { value: 'swaps', label: 'Swaps' },
        { value: 'drops', label: 'Drops' },
      ],
    },
    {
      type: 'select',
      label: 'Shift Status',
      name: 'status',
      options: [
        { value: 'all', label: 'All Status' },
        { value: 'completed', label: 'Completed' },
        { value: 'scheduled', label: 'Scheduled' },
        { value: 'swapped', label: 'Swapped' },
        { value: 'cancelled', label: 'Cancelled' },
      ],
      placeholder: 'Select Shift Status',
    },
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      options: [
        { value: 'all', label: 'All Departments' },
        { value: 'Emergency', label: 'Emergency' },
        { value: 'ICU', label: 'ICU' },
        { value: 'Surgery', label: 'Surgery' },
        { value: 'Pediatrics', label: 'Pediatrics' },
      ],
      placeholder: 'Select Department',
    },
    {
      type: 'select',
      label: 'Date Period',
      name: 'date_period',
      options: [
        { value: 'week', label: 'This Week' },
        { value: 'month', label: 'This Month' },
        { value: 'quarter', label: 'This Quarter' },
        { value: 'year', label: 'This Year' },
        { value: 'custom', label: 'Custom Range' },
      ],
      placeholder: 'Select Date Period',
    },
  ];

  if (filters.date_period === 'custom') {
    filterFields.push({
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    });
  }

  // No useEffect needed since we're using static data

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setCurrentPage(1); // Reset pagination when changing tabs

    // Reset both filter states to default when changing tabs
    const defaultFilters = {
      dateFrom: '',
      dateTo: '',
      status: '',
      department: '',
      type: 'all',
      date_period: 'month',
      leave_type: 'all',
      leave_status: 'all',
      dayoff_type: 'all',
      is_recurring: 'all',
    };

    setFilters(defaultFilters);
    setAppliedFilters(defaultFilters);

    // Reset sorting
    setSortField('');
    setSortOrder('asc');
  };

  // Pagination handlers
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
  };

  // Sorting handler
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortOrder(direction);
    setCurrentPage(1);
  };

  // Filter and sort data utility functions
  const filterData = (data, activeFilters, tabType) => {
    let filteredData = [...data];

    // Apply filters based on tab type
    if (tabType === 'shifts') {
      if (activeFilters.status && activeFilters.status !== 'all') {
        filteredData = filteredData.filter(
          (item) => item.status === activeFilters.status
        );
      }
      if (activeFilters.department && activeFilters.department !== 'all') {
        filteredData = filteredData.filter(
          (item) => item.department === activeFilters.department
        );
      }
      if (activeFilters.type && activeFilters.type !== 'all') {
        if (activeFilters.type === 'swaps') {
          filteredData = filteredData.filter(
            (item) => item.status === 'swapped'
          );
        } else if (activeFilters.type === 'drops') {
          filteredData = filteredData.filter((item) => item.canDrop === true);
        }
      }
    } else if (tabType === 'leaves') {
      if (activeFilters.leave_type && activeFilters.leave_type !== 'all') {
        filteredData = filteredData.filter(
          (item) => item.type === activeFilters.leave_type
        );
      }
      if (activeFilters.leave_status && activeFilters.leave_status !== 'all') {
        filteredData = filteredData.filter(
          (item) => item.status === activeFilters.leave_status
        );
      }
    } else if (tabType === 'daysoff') {
      if (activeFilters.dayoff_type && activeFilters.dayoff_type !== 'all') {
        filteredData = filteredData.filter(
          (item) => item.type === activeFilters.dayoff_type
        );
      }
      if (activeFilters.is_recurring && activeFilters.is_recurring !== 'all') {
        const isRecurring = activeFilters.is_recurring === 'true';
        filteredData = filteredData.filter(
          (item) => item.isRecurring === isRecurring
        );
      }
    }

    // Apply date range filters if present
    if (activeFilters.dateFrom && activeFilters.dateTo) {
      const fromDate = new Date(activeFilters.dateFrom);
      const toDate = new Date(activeFilters.dateTo);

      filteredData = filteredData.filter((item) => {
        const itemDate = new Date(item.date || item.startDate);
        return itemDate >= fromDate && itemDate <= toDate;
      });
    }

    return filteredData;
  };

  const sortData = (data, field, order) => {
    if (!field) return data;

    return [...data].sort((a, b) => {
      let aValue = a[field];
      let bValue = b[field];

      // Handle different data types
      if (
        field === 'date' ||
        field === 'startDate' ||
        field === 'endDate' ||
        field === 'appliedDate'
      ) {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return order === 'asc' ? -1 : 1;
      if (aValue > bValue) return order === 'asc' ? 1 : -1;
      return 0;
    });
  };

  const getProcessedData = (tabType) => {
    let rawData;
    switch (tabType) {
      case 'shifts':
        rawData = staticEmployeeData.shifts;
        break;
      case 'leaves':
        rawData = staticEmployeeData.leaves;
        break;
      case 'daysoff':
        rawData = staticEmployeeData.daysOff;
        break;
      default:
        rawData = staticEmployeeData.shifts;
    }

    // Apply filters using appliedFilters (only updates when Apply button is clicked)
    let processedData = filterData(rawData, appliedFilters, tabType);

    // Apply sorting
    processedData = sortData(processedData, sortField, sortOrder);

    return processedData;
  };

  // Filter handlers
  const handleApplyFilters = (newFilters) => {
    // Update both filter states and applied filters when Apply button is clicked
    setFilters(newFilters);
    setAppliedFilters(newFilters);
    setCurrentPage(1);
  };

  const handleFieldChange = (fieldName, value) => {
    // Only update the filter form state, don't apply filters immediately
    // Filters will be applied when user clicks the Apply/Filter button
    const updatedFilters = { ...filters, [fieldName]: value };
    setFilters(updatedFilters);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-GB', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return 'N/A';
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Render functions for each tab
  const renderShiftsTab = () => {
    const processedData = getProcessedData('shifts');
    return (
      <Box>
        <FilterCollapse
          fields={filterFields}
          onApply={handleApplyFilters}
          initialValues={filters}
          onFieldChange={handleFieldChange}
        />

        <Box className="shifts-table-container" mt={2}>
          <CommonTable
            columns={shiftsColumns}
            data={processedData}
            loading={loading}
            currentPage={currentPage}
            totalCount={processedData.length}
            pageSize={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            onSort={handleSort}
            sortOrder={{ field: sortField, direction: sortOrder }}
            showPagination={true}
            actionMenuItems={[]}
          />
        </Box>
      </Box>
    );
  };

  const renderLeavesTab = () => {
    const processedData = getProcessedData('leaves');
    return (
      <Box className="leaves-table-container">
        <FilterCollapse
          fields={[
            {
              type: 'select',
              label: 'Leave Type',
              name: 'leave_type',
              options: [
                { value: 'all', label: 'All Types' },
                { value: 'annual', label: 'Annual Leave' },
                { value: 'sick', label: 'Sick Leave' },
                { value: 'personal', label: 'Personal Leave' },
              ],
              placeholder: 'Select Leave Type',
            },
            {
              type: 'select',
              label: 'Status',
              name: 'leave_status',
              options: [
                { value: 'all', label: 'All Status' },
                { value: 'approved', label: 'Approved' },
                { value: 'pending', label: 'Pending' },
                { value: 'rejected', label: 'Rejected' },
              ],
              placeholder: 'Select Status',
            },
          ]}
          onApply={handleApplyFilters}
          initialValues={filters}
          onFieldChange={handleFieldChange}
        />
        <Box className="shifts-table-container" mt={2}>
          <CommonTable
            columns={leavesColumns}
            data={processedData}
            loading={loading}
            currentPage={currentPage}
            totalCount={processedData.length}
            pageSize={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            onSort={handleSort}
            sortOrder={{ field: sortField, direction: sortOrder }}
            showPagination={true}
            actionMenuItems={[]}
          />
        </Box>
      </Box>
    );
  };

  const renderDaysOffTab = () => {
    const processedData = getProcessedData('daysoff');
    return (
      <Box className="daysoff-table-container">
        <FilterCollapse
          fields={[
            {
              type: 'select',
              label: 'Day Off Type',
              name: 'dayoff_type',
              options: [
                { value: 'all', label: 'All Types' },
                { value: 'weekend', label: 'Weekend' },
                { value: 'holiday', label: 'Holiday' },
                { value: 'personal', label: 'Personal Day' },
              ],
              placeholder: 'Select Day Off Type',
            },
            {
              type: 'select',
              label: 'Recurring',
              name: 'is_recurring',
              options: [
                { value: 'all', label: 'All' },
                { value: 'true', label: 'Recurring' },
                { value: 'false', label: 'One-time' },
              ],
              placeholder: 'Select Recurring',
            },
          ]}
          onApply={handleApplyFilters}
          initialValues={filters}
          onFieldChange={handleFieldChange}
        />
        <Box className="shifts-table-container" mt={2}>
          <CommonTable
            columns={daysOffColumns}
            data={processedData}
            loading={loading}
            currentPage={currentPage}
            totalCount={processedData.length}
            pageSize={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            onSort={handleSort}
            sortOrder={{ field: sortField, direction: sortOrder }}
            showPagination={true}
            actionMenuItems={[]}
          />
        </Box>
      </Box>
    );
  };

  const getCurrentTabContent = () => {
    switch (activeTab) {
      case 'shifts':
        return renderShiftsTab();
      case 'leaves':
        return renderLeavesTab();
      case 'daysoff':
        return renderDaysOffTab();
      default:
        return renderShiftsTab();
    }
  };

  // Handle case when no employee data is available
  if (!isOpen || !employeeData) {
    return null;
  }

  // Create the title with employee info
  const dialogTitle = (
    <Box className="employee-header-info">
      <Avatar className="employee-avatar" src={staticEmployeeData.user.avatar}>
        {staticEmployeeData.user.name?.charAt(0) || <PersonIcon />}
      </Avatar>
      <Box className="employee-details">
        <Typography variant="h6" className="employee-name">
          {staticEmployeeData.user.name}
        </Typography>
        <Typography variant="body2" className="employee-id">
          ID: {staticEmployeeData.user.employeeId}
        </Typography>
        <Typography variant="body2" className="employee-role">
          {staticEmployeeData.user.role} • {staticEmployeeData.user.department}
        </Typography>
        <Typography variant="body2" className="employee-join-date">
          Joined: {formatDate(staticEmployeeData.user.joinDate)}
        </Typography>
      </Box>
    </Box>
  );

  // Create the content for the dialog
  const dialogContent = (
    <Box className="rota-details-content">
      {loading ? (
        <ContentLoader />
      ) : (
        <>
          {/* Employee Summary */}
          <Card className="summary-card">
            <CardContent>
              {/* <Typography variant="h6" gutterBottom>
                Summary
              </Typography> */}
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Total Shifts
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {staticEmployeeData.quickStats.totalShifts}
                    </Typography>
                  </Box>
                </Grid>
                {/* <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Completed Shifts
                    </Typography>
                    <Typography variant="h6" color="success">
                      {staticEmployeeData.quickStats.completedShifts}
                    </Typography>
                  </Box>
                </Grid> */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Total Hours
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {staticEmployeeData.quickStats.totalHours}
                    </Typography>
                  </Box>
                </Grid>
                {/* <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Overtime Hours
                    </Typography>
                    <Typography variant="h6" color="warning">
                      {staticEmployeeData.quickStats.overtimeHours}
                    </Typography>
                  </Box>
                </Grid> */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Leave Days
                    </Typography>
                    <Typography variant="h6" color="error">
                      {staticEmployeeData.quickStats.leaveDays}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box className="summary-item">
                    <Typography variant="body2" color="textSecondary">
                      Days Off
                    </Typography>
                    <Typography variant="h6" color="info">
                      {staticEmployeeData.quickStats.daysOff}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Divider sx={{ my: 3 }} />

          {/* Tabs Section */}
          <Box className="details-tabs-section">
            <CustomTabs
              tabs={tabs}
              initialTab={activeTab}
              onTabChange={handleTabChange}
            />
            <Box className="tab-content" sx={{ mt: 2 }}>
              {getCurrentTabContent()}
            </Box>
          </Box>
        </>
      )}
    </Box>
  );

  return (
    <DialogBox
      open={isOpen}
      handleClose={onClose}
      title={dialogTitle}
      content={dialogContent}
      className="fullscreen-dialog-box-container rota-details-dialog"
      onCloseStatus={true}
    />
  );
};

export default RotaReportDetails;
