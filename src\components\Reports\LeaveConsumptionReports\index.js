'use client';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { leaveService } from '@/services/leaveService';
import '../reports.scss';

export default function LeaveConsumptionReports({ onFiltersUpdate }) {
  // State management
  const [loader, setLoader] = useState(false);
  const [leaveConsumptionList, setLeaveConsumptionList] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({});
  const [sortOrder, setSortOrder] = useState({ key: '', value: '' });
  const [leaveTypes, setLeaveTypes] = useState([]); // ✅ Store leave types for dynamic columns

  const currentYear = new Date().getFullYear();

  const options = Array.from({ length: 3 }, (_, i) => {
    const year = currentYear - 2 + i; // Start from currentYear - 2
    return { label: year.toString(), value: year.toString() };
  });

  // Filter fields configuration - simplified to only year filter
  const filterFields = useMemo(
    () => [
      {
        type: 'select',
        label: 'Year',
        name: 'year',
        placeholder: 'Select Year',
        options: options,
      },
    ],
    []
  );

  // Dynamic columns configuration based on leave types from API
  const columns = useMemo(() => {
    const baseColumns = [
      {
        header: 'Month',
        accessor: 'month',
        sortable: false,
        renderCell: (value) =>
          value !== null && value !== undefined ? value : '-',
      },
    ];

    // Add dynamic columns based on leave types from API
    const dynamicColumns = leaveTypes.map((leaveType) => ({
      header: leaveType.name, // ✅ Use dynamic name from API
      accessor: leaveType.key, // ✅ Use dynamic key from API
      sortable: false,
      renderCell: (value) =>
        value !== null && value !== undefined ? value : '-',
    }));

    return [...baseColumns, ...dynamicColumns];
  }, [leaveTypes]); // ✅ Regenerate columns when leaveTypes change

  // Get leave usage report using new API
  const getLeaveUsageReport = useCallback(
    async (year = '', showLoader = true) => {
      if (showLoader) setLoader(true);
      try {
        const params = {
          year,
        };

        const response = await leaveService.getLeaveUsageReport(params);

        if (response.success) {
          // ✅ leaveService returns processed data structure:
          // response.data = monthly_report array
          // response.leaveTypes = leave_types array

          // ✅ Set leave types for dynamic columns
          setLeaveTypes(response.leaveTypes || []);

          // ✅ Set monthly report data for table (keep original API order)
          setLeaveConsumptionList(response.data || []);
          setTotalCount(response.count || 0);
        } else {
          setLeaveConsumptionList([]);
          setLeaveTypes([]);
          setTotalCount(0);
          setApiMessage('error', response.message || 'Failed to fetch data');
        }
        setLoader(false);
      } catch (error) {
        console.error('API Call Error:', error);
        setLoader(false);
        setLeaveConsumptionList([]);
        setLeaveTypes([]);
        setTotalCount(0);
        setApiMessage(
          'error',
          error?.message || 'Failed to fetch leave usage report'
        );
      }
    },
    []
  );

  // Handle filter application - simplified for year filter only
  const handleApplyFilters = (values) => {
    setFilters(values);

    // Prepare filters for export
    const exportFilters = {
      year: values?.year || '',
      sortBy: sortOrder?.key || '',
      sortOrder: sortOrder?.value || '',
      totalCount: totalCount || 0,
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }

    getLeaveUsageReport(values?.year || '', false);
  };

  // useEffect hooks
  useEffect(() => {
    // Load initial data with default year 2025
    getLeaveUsageReport('2025');
  }, [getLeaveUsageReport]);

  // Update parent with initial filters on mount
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        year: '2025', // Default year
      };
      onFiltersUpdate(exportFilters);
    }
  }, [onFiltersUpdate]); // Run only when onFiltersUpdate changes (component mount)

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        year: filters?.year || '2025',
      };

      // Add sorting parameters if available
      if (sortOrder?.key) exportFilters.sort_by = sortOrder.key;
      if (sortOrder?.value) exportFilters.sort_order = sortOrder.value;

      onFiltersUpdate(exportFilters);
    }
  }, [filters, sortOrder, onFiltersUpdate]); // Include dependencies

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        buttonText="Apply Filters"
        initialValues={filters}
      />

      <Box className="report-table-container">
        {loader ? (
          <ContentLoader />
        ) : (
          <CommonTable
            columns={columns}
            data={leaveConsumptionList}
            totalCount={totalCount}
            currentPage={1}
            showPagination={false}
            onSort={(key, order) => {
              setSortOrder({ key, value: order });
              // Update export filters when sorting changes
              if (onFiltersUpdate) {
                const exportFilters = {
                  year: filters?.year || '2025',
                  sort_by: key,
                  sort_order: order,
                };
                onFiltersUpdate(exportFilters);
              }
            }}
          />
        )}
      </Box>
    </Box>
  );
}
